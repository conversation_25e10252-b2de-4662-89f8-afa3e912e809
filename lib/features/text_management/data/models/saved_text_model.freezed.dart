// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'saved_text_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

SavedTextModel _$SavedTextModelFromJson(Map<String, dynamic> json) {
  return _SavedTextModel.fromJson(json);
}

/// @nodoc
mixin _$SavedTextModel {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get content => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this SavedTextModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SavedTextModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SavedTextModelCopyWith<SavedTextModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SavedTextModelCopyWith<$Res> {
  factory $SavedTextModelCopyWith(
    SavedTextModel value,
    $Res Function(SavedTextModel) then,
  ) = _$SavedTextModelCopyWithImpl<$Res, SavedTextModel>;
  @useResult
  $Res call({
    String id,
    String title,
    String content,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class _$SavedTextModelCopyWithImpl<$Res, $Val extends SavedTextModel>
    implements $SavedTextModelCopyWith<$Res> {
  _$SavedTextModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SavedTextModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? content = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            title: null == title
                ? _value.title
                : title // ignore: cast_nullable_to_non_nullable
                      as String,
            content: null == content
                ? _value.content
                : content // ignore: cast_nullable_to_non_nullable
                      as String,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            updatedAt: null == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$SavedTextModelImplCopyWith<$Res>
    implements $SavedTextModelCopyWith<$Res> {
  factory _$$SavedTextModelImplCopyWith(
    _$SavedTextModelImpl value,
    $Res Function(_$SavedTextModelImpl) then,
  ) = __$$SavedTextModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String title,
    String content,
    DateTime createdAt,
    DateTime updatedAt,
  });
}

/// @nodoc
class __$$SavedTextModelImplCopyWithImpl<$Res>
    extends _$SavedTextModelCopyWithImpl<$Res, _$SavedTextModelImpl>
    implements _$$SavedTextModelImplCopyWith<$Res> {
  __$$SavedTextModelImplCopyWithImpl(
    _$SavedTextModelImpl _value,
    $Res Function(_$SavedTextModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of SavedTextModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? content = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(
      _$SavedTextModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _value.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        content: null == content
            ? _value.content
            : content // ignore: cast_nullable_to_non_nullable
                  as String,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        updatedAt: null == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$SavedTextModelImpl extends _SavedTextModel {
  const _$SavedTextModelImpl({
    required this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.updatedAt,
  }) : super._();

  factory _$SavedTextModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SavedTextModelImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String content;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'SavedTextModel(id: $id, title: $title, content: $content, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SavedTextModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, title, content, createdAt, updatedAt);

  /// Create a copy of SavedTextModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SavedTextModelImplCopyWith<_$SavedTextModelImpl> get copyWith =>
      __$$SavedTextModelImplCopyWithImpl<_$SavedTextModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$SavedTextModelImplToJson(this);
  }
}

abstract class _SavedTextModel extends SavedTextModel {
  const factory _SavedTextModel({
    required final String id,
    required final String title,
    required final String content,
    required final DateTime createdAt,
    required final DateTime updatedAt,
  }) = _$SavedTextModelImpl;
  const _SavedTextModel._() : super._();

  factory _SavedTextModel.fromJson(Map<String, dynamic> json) =
      _$SavedTextModelImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get content;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of SavedTextModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SavedTextModelImplCopyWith<_$SavedTextModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
