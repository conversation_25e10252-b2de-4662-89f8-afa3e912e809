// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'text_management_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$TextManagementState {
  List<SavedText> get savedTexts => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isAdding => throw _privateConstructorUsedError;
  bool get isDeleting => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  String? get successMessage => throw _privateConstructorUsedError;

  /// Create a copy of TextManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TextManagementStateCopyWith<TextManagementState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TextManagementStateCopyWith<$Res> {
  factory $TextManagementStateCopyWith(
    TextManagementState value,
    $Res Function(TextManagementState) then,
  ) = _$TextManagementStateCopyWithImpl<$Res, TextManagementState>;
  @useResult
  $Res call({
    List<SavedText> savedTexts,
    bool isLoading,
    bool isAdding,
    bool isDeleting,
    String? errorMessage,
    String? successMessage,
  });
}

/// @nodoc
class _$TextManagementStateCopyWithImpl<$Res, $Val extends TextManagementState>
    implements $TextManagementStateCopyWith<$Res> {
  _$TextManagementStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TextManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? savedTexts = null,
    Object? isLoading = null,
    Object? isAdding = null,
    Object? isDeleting = null,
    Object? errorMessage = freezed,
    Object? successMessage = freezed,
  }) {
    return _then(
      _value.copyWith(
            savedTexts: null == savedTexts
                ? _value.savedTexts
                : savedTexts // ignore: cast_nullable_to_non_nullable
                      as List<SavedText>,
            isLoading: null == isLoading
                ? _value.isLoading
                : isLoading // ignore: cast_nullable_to_non_nullable
                      as bool,
            isAdding: null == isAdding
                ? _value.isAdding
                : isAdding // ignore: cast_nullable_to_non_nullable
                      as bool,
            isDeleting: null == isDeleting
                ? _value.isDeleting
                : isDeleting // ignore: cast_nullable_to_non_nullable
                      as bool,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            successMessage: freezed == successMessage
                ? _value.successMessage
                : successMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$TextManagementStateImplCopyWith<$Res>
    implements $TextManagementStateCopyWith<$Res> {
  factory _$$TextManagementStateImplCopyWith(
    _$TextManagementStateImpl value,
    $Res Function(_$TextManagementStateImpl) then,
  ) = __$$TextManagementStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    List<SavedText> savedTexts,
    bool isLoading,
    bool isAdding,
    bool isDeleting,
    String? errorMessage,
    String? successMessage,
  });
}

/// @nodoc
class __$$TextManagementStateImplCopyWithImpl<$Res>
    extends _$TextManagementStateCopyWithImpl<$Res, _$TextManagementStateImpl>
    implements _$$TextManagementStateImplCopyWith<$Res> {
  __$$TextManagementStateImplCopyWithImpl(
    _$TextManagementStateImpl _value,
    $Res Function(_$TextManagementStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of TextManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? savedTexts = null,
    Object? isLoading = null,
    Object? isAdding = null,
    Object? isDeleting = null,
    Object? errorMessage = freezed,
    Object? successMessage = freezed,
  }) {
    return _then(
      _$TextManagementStateImpl(
        savedTexts: null == savedTexts
            ? _value._savedTexts
            : savedTexts // ignore: cast_nullable_to_non_nullable
                  as List<SavedText>,
        isLoading: null == isLoading
            ? _value.isLoading
            : isLoading // ignore: cast_nullable_to_non_nullable
                  as bool,
        isAdding: null == isAdding
            ? _value.isAdding
            : isAdding // ignore: cast_nullable_to_non_nullable
                  as bool,
        isDeleting: null == isDeleting
            ? _value.isDeleting
            : isDeleting // ignore: cast_nullable_to_non_nullable
                  as bool,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        successMessage: freezed == successMessage
            ? _value.successMessage
            : successMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$TextManagementStateImpl implements _TextManagementState {
  const _$TextManagementStateImpl({
    final List<SavedText> savedTexts = const [],
    this.isLoading = false,
    this.isAdding = false,
    this.isDeleting = false,
    this.errorMessage,
    this.successMessage,
  }) : _savedTexts = savedTexts;

  final List<SavedText> _savedTexts;
  @override
  @JsonKey()
  List<SavedText> get savedTexts {
    if (_savedTexts is EqualUnmodifiableListView) return _savedTexts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_savedTexts);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isAdding;
  @override
  @JsonKey()
  final bool isDeleting;
  @override
  final String? errorMessage;
  @override
  final String? successMessage;

  @override
  String toString() {
    return 'TextManagementState(savedTexts: $savedTexts, isLoading: $isLoading, isAdding: $isAdding, isDeleting: $isDeleting, errorMessage: $errorMessage, successMessage: $successMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TextManagementStateImpl &&
            const DeepCollectionEquality().equals(
              other._savedTexts,
              _savedTexts,
            ) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isAdding, isAdding) ||
                other.isAdding == isAdding) &&
            (identical(other.isDeleting, isDeleting) ||
                other.isDeleting == isDeleting) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.successMessage, successMessage) ||
                other.successMessage == successMessage));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_savedTexts),
    isLoading,
    isAdding,
    isDeleting,
    errorMessage,
    successMessage,
  );

  /// Create a copy of TextManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TextManagementStateImplCopyWith<_$TextManagementStateImpl> get copyWith =>
      __$$TextManagementStateImplCopyWithImpl<_$TextManagementStateImpl>(
        this,
        _$identity,
      );
}

abstract class _TextManagementState implements TextManagementState {
  const factory _TextManagementState({
    final List<SavedText> savedTexts,
    final bool isLoading,
    final bool isAdding,
    final bool isDeleting,
    final String? errorMessage,
    final String? successMessage,
  }) = _$TextManagementStateImpl;

  @override
  List<SavedText> get savedTexts;
  @override
  bool get isLoading;
  @override
  bool get isAdding;
  @override
  bool get isDeleting;
  @override
  String? get errorMessage;
  @override
  String? get successMessage;

  /// Create a copy of TextManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TextManagementStateImplCopyWith<_$TextManagementStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
