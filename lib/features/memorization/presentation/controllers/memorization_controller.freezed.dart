// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'memorization_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$MemorizationState {
  MemorizationSession? get session => throw _privateConstructorUsedError;
  List<String> get revealedWords => throw _privateConstructorUsedError;
  int get currentWordIndex => throw _privateConstructorUsedError;
  bool get isListening => throw _privateConstructorUsedError;
  bool get isInitializing => throw _privateConstructorUsedError;
  bool get isCompleted => throw _privateConstructorUsedError;
  bool get hasError => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  String? get lastSpokenWord => throw _privateConstructorUsedError;
  WordMatchResult? get lastMatchResult => throw _privateConstructorUsedError;

  /// Create a copy of MemorizationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MemorizationStateCopyWith<MemorizationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MemorizationStateCopyWith<$Res> {
  factory $MemorizationStateCopyWith(
    MemorizationState value,
    $Res Function(MemorizationState) then,
  ) = _$MemorizationStateCopyWithImpl<$Res, MemorizationState>;
  @useResult
  $Res call({
    MemorizationSession? session,
    List<String> revealedWords,
    int currentWordIndex,
    bool isListening,
    bool isInitializing,
    bool isCompleted,
    bool hasError,
    String? errorMessage,
    String? lastSpokenWord,
    WordMatchResult? lastMatchResult,
  });

  $MemorizationSessionCopyWith<$Res>? get session;
  $WordMatchResultCopyWith<$Res>? get lastMatchResult;
}

/// @nodoc
class _$MemorizationStateCopyWithImpl<$Res, $Val extends MemorizationState>
    implements $MemorizationStateCopyWith<$Res> {
  _$MemorizationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MemorizationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? session = freezed,
    Object? revealedWords = null,
    Object? currentWordIndex = null,
    Object? isListening = null,
    Object? isInitializing = null,
    Object? isCompleted = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? lastSpokenWord = freezed,
    Object? lastMatchResult = freezed,
  }) {
    return _then(
      _value.copyWith(
            session: freezed == session
                ? _value.session
                : session // ignore: cast_nullable_to_non_nullable
                      as MemorizationSession?,
            revealedWords: null == revealedWords
                ? _value.revealedWords
                : revealedWords // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            currentWordIndex: null == currentWordIndex
                ? _value.currentWordIndex
                : currentWordIndex // ignore: cast_nullable_to_non_nullable
                      as int,
            isListening: null == isListening
                ? _value.isListening
                : isListening // ignore: cast_nullable_to_non_nullable
                      as bool,
            isInitializing: null == isInitializing
                ? _value.isInitializing
                : isInitializing // ignore: cast_nullable_to_non_nullable
                      as bool,
            isCompleted: null == isCompleted
                ? _value.isCompleted
                : isCompleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            hasError: null == hasError
                ? _value.hasError
                : hasError // ignore: cast_nullable_to_non_nullable
                      as bool,
            errorMessage: freezed == errorMessage
                ? _value.errorMessage
                : errorMessage // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastSpokenWord: freezed == lastSpokenWord
                ? _value.lastSpokenWord
                : lastSpokenWord // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastMatchResult: freezed == lastMatchResult
                ? _value.lastMatchResult
                : lastMatchResult // ignore: cast_nullable_to_non_nullable
                      as WordMatchResult?,
          )
          as $Val,
    );
  }

  /// Create a copy of MemorizationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MemorizationSessionCopyWith<$Res>? get session {
    if (_value.session == null) {
      return null;
    }

    return $MemorizationSessionCopyWith<$Res>(_value.session!, (value) {
      return _then(_value.copyWith(session: value) as $Val);
    });
  }

  /// Create a copy of MemorizationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WordMatchResultCopyWith<$Res>? get lastMatchResult {
    if (_value.lastMatchResult == null) {
      return null;
    }

    return $WordMatchResultCopyWith<$Res>(_value.lastMatchResult!, (value) {
      return _then(_value.copyWith(lastMatchResult: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MemorizationStateImplCopyWith<$Res>
    implements $MemorizationStateCopyWith<$Res> {
  factory _$$MemorizationStateImplCopyWith(
    _$MemorizationStateImpl value,
    $Res Function(_$MemorizationStateImpl) then,
  ) = __$$MemorizationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    MemorizationSession? session,
    List<String> revealedWords,
    int currentWordIndex,
    bool isListening,
    bool isInitializing,
    bool isCompleted,
    bool hasError,
    String? errorMessage,
    String? lastSpokenWord,
    WordMatchResult? lastMatchResult,
  });

  @override
  $MemorizationSessionCopyWith<$Res>? get session;
  @override
  $WordMatchResultCopyWith<$Res>? get lastMatchResult;
}

/// @nodoc
class __$$MemorizationStateImplCopyWithImpl<$Res>
    extends _$MemorizationStateCopyWithImpl<$Res, _$MemorizationStateImpl>
    implements _$$MemorizationStateImplCopyWith<$Res> {
  __$$MemorizationStateImplCopyWithImpl(
    _$MemorizationStateImpl _value,
    $Res Function(_$MemorizationStateImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MemorizationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? session = freezed,
    Object? revealedWords = null,
    Object? currentWordIndex = null,
    Object? isListening = null,
    Object? isInitializing = null,
    Object? isCompleted = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
    Object? lastSpokenWord = freezed,
    Object? lastMatchResult = freezed,
  }) {
    return _then(
      _$MemorizationStateImpl(
        session: freezed == session
            ? _value.session
            : session // ignore: cast_nullable_to_non_nullable
                  as MemorizationSession?,
        revealedWords: null == revealedWords
            ? _value._revealedWords
            : revealedWords // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        currentWordIndex: null == currentWordIndex
            ? _value.currentWordIndex
            : currentWordIndex // ignore: cast_nullable_to_non_nullable
                  as int,
        isListening: null == isListening
            ? _value.isListening
            : isListening // ignore: cast_nullable_to_non_nullable
                  as bool,
        isInitializing: null == isInitializing
            ? _value.isInitializing
            : isInitializing // ignore: cast_nullable_to_non_nullable
                  as bool,
        isCompleted: null == isCompleted
            ? _value.isCompleted
            : isCompleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        hasError: null == hasError
            ? _value.hasError
            : hasError // ignore: cast_nullable_to_non_nullable
                  as bool,
        errorMessage: freezed == errorMessage
            ? _value.errorMessage
            : errorMessage // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastSpokenWord: freezed == lastSpokenWord
            ? _value.lastSpokenWord
            : lastSpokenWord // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastMatchResult: freezed == lastMatchResult
            ? _value.lastMatchResult
            : lastMatchResult // ignore: cast_nullable_to_non_nullable
                  as WordMatchResult?,
      ),
    );
  }
}

/// @nodoc

class _$MemorizationStateImpl implements _MemorizationState {
  const _$MemorizationStateImpl({
    this.session,
    final List<String> revealedWords = const [],
    this.currentWordIndex = 0,
    this.isListening = false,
    this.isInitializing = false,
    this.isCompleted = false,
    this.hasError = false,
    this.errorMessage,
    this.lastSpokenWord,
    this.lastMatchResult,
  }) : _revealedWords = revealedWords;

  @override
  final MemorizationSession? session;
  final List<String> _revealedWords;
  @override
  @JsonKey()
  List<String> get revealedWords {
    if (_revealedWords is EqualUnmodifiableListView) return _revealedWords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_revealedWords);
  }

  @override
  @JsonKey()
  final int currentWordIndex;
  @override
  @JsonKey()
  final bool isListening;
  @override
  @JsonKey()
  final bool isInitializing;
  @override
  @JsonKey()
  final bool isCompleted;
  @override
  @JsonKey()
  final bool hasError;
  @override
  final String? errorMessage;
  @override
  final String? lastSpokenWord;
  @override
  final WordMatchResult? lastMatchResult;

  @override
  String toString() {
    return 'MemorizationState(session: $session, revealedWords: $revealedWords, currentWordIndex: $currentWordIndex, isListening: $isListening, isInitializing: $isInitializing, isCompleted: $isCompleted, hasError: $hasError, errorMessage: $errorMessage, lastSpokenWord: $lastSpokenWord, lastMatchResult: $lastMatchResult)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MemorizationStateImpl &&
            (identical(other.session, session) || other.session == session) &&
            const DeepCollectionEquality().equals(
              other._revealedWords,
              _revealedWords,
            ) &&
            (identical(other.currentWordIndex, currentWordIndex) ||
                other.currentWordIndex == currentWordIndex) &&
            (identical(other.isListening, isListening) ||
                other.isListening == isListening) &&
            (identical(other.isInitializing, isInitializing) ||
                other.isInitializing == isInitializing) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.lastSpokenWord, lastSpokenWord) ||
                other.lastSpokenWord == lastSpokenWord) &&
            (identical(other.lastMatchResult, lastMatchResult) ||
                other.lastMatchResult == lastMatchResult));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    session,
    const DeepCollectionEquality().hash(_revealedWords),
    currentWordIndex,
    isListening,
    isInitializing,
    isCompleted,
    hasError,
    errorMessage,
    lastSpokenWord,
    lastMatchResult,
  );

  /// Create a copy of MemorizationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MemorizationStateImplCopyWith<_$MemorizationStateImpl> get copyWith =>
      __$$MemorizationStateImplCopyWithImpl<_$MemorizationStateImpl>(
        this,
        _$identity,
      );
}

abstract class _MemorizationState implements MemorizationState {
  const factory _MemorizationState({
    final MemorizationSession? session,
    final List<String> revealedWords,
    final int currentWordIndex,
    final bool isListening,
    final bool isInitializing,
    final bool isCompleted,
    final bool hasError,
    final String? errorMessage,
    final String? lastSpokenWord,
    final WordMatchResult? lastMatchResult,
  }) = _$MemorizationStateImpl;

  @override
  MemorizationSession? get session;
  @override
  List<String> get revealedWords;
  @override
  int get currentWordIndex;
  @override
  bool get isListening;
  @override
  bool get isInitializing;
  @override
  bool get isCompleted;
  @override
  bool get hasError;
  @override
  String? get errorMessage;
  @override
  String? get lastSpokenWord;
  @override
  WordMatchResult? get lastMatchResult;

  /// Create a copy of MemorizationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MemorizationStateImplCopyWith<_$MemorizationStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
