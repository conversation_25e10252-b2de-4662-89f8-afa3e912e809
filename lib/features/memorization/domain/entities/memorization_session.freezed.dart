// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'memorization_session.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$MemorizationSession {
  String get id => throw _privateConstructorUsedError;
  String get textId => throw _privateConstructorUsedError;
  List<String> get words => throw _privateConstructorUsedError;
  int get currentWordIndex => throw _privateConstructorUsedError;
  List<String> get completedWords => throw _privateConstructorUsedError;
  bool get isCompleted => throw _privateConstructorUsedError;
  DateTime get startedAt => throw _privateConstructorUsedError;
  DateTime? get completedAt => throw _privateConstructorUsedError;

  /// Create a copy of MemorizationSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MemorizationSessionCopyWith<MemorizationSession> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MemorizationSessionCopyWith<$Res> {
  factory $MemorizationSessionCopyWith(
    MemorizationSession value,
    $Res Function(MemorizationSession) then,
  ) = _$MemorizationSessionCopyWithImpl<$Res, MemorizationSession>;
  @useResult
  $Res call({
    String id,
    String textId,
    List<String> words,
    int currentWordIndex,
    List<String> completedWords,
    bool isCompleted,
    DateTime startedAt,
    DateTime? completedAt,
  });
}

/// @nodoc
class _$MemorizationSessionCopyWithImpl<$Res, $Val extends MemorizationSession>
    implements $MemorizationSessionCopyWith<$Res> {
  _$MemorizationSessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MemorizationSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? textId = null,
    Object? words = null,
    Object? currentWordIndex = null,
    Object? completedWords = null,
    Object? isCompleted = null,
    Object? startedAt = null,
    Object? completedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            textId: null == textId
                ? _value.textId
                : textId // ignore: cast_nullable_to_non_nullable
                      as String,
            words: null == words
                ? _value.words
                : words // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            currentWordIndex: null == currentWordIndex
                ? _value.currentWordIndex
                : currentWordIndex // ignore: cast_nullable_to_non_nullable
                      as int,
            completedWords: null == completedWords
                ? _value.completedWords
                : completedWords // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            isCompleted: null == isCompleted
                ? _value.isCompleted
                : isCompleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            startedAt: null == startedAt
                ? _value.startedAt
                : startedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            completedAt: freezed == completedAt
                ? _value.completedAt
                : completedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MemorizationSessionImplCopyWith<$Res>
    implements $MemorizationSessionCopyWith<$Res> {
  factory _$$MemorizationSessionImplCopyWith(
    _$MemorizationSessionImpl value,
    $Res Function(_$MemorizationSessionImpl) then,
  ) = __$$MemorizationSessionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String textId,
    List<String> words,
    int currentWordIndex,
    List<String> completedWords,
    bool isCompleted,
    DateTime startedAt,
    DateTime? completedAt,
  });
}

/// @nodoc
class __$$MemorizationSessionImplCopyWithImpl<$Res>
    extends _$MemorizationSessionCopyWithImpl<$Res, _$MemorizationSessionImpl>
    implements _$$MemorizationSessionImplCopyWith<$Res> {
  __$$MemorizationSessionImplCopyWithImpl(
    _$MemorizationSessionImpl _value,
    $Res Function(_$MemorizationSessionImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MemorizationSession
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? textId = null,
    Object? words = null,
    Object? currentWordIndex = null,
    Object? completedWords = null,
    Object? isCompleted = null,
    Object? startedAt = null,
    Object? completedAt = freezed,
  }) {
    return _then(
      _$MemorizationSessionImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        textId: null == textId
            ? _value.textId
            : textId // ignore: cast_nullable_to_non_nullable
                  as String,
        words: null == words
            ? _value._words
            : words // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        currentWordIndex: null == currentWordIndex
            ? _value.currentWordIndex
            : currentWordIndex // ignore: cast_nullable_to_non_nullable
                  as int,
        completedWords: null == completedWords
            ? _value._completedWords
            : completedWords // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        isCompleted: null == isCompleted
            ? _value.isCompleted
            : isCompleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        startedAt: null == startedAt
            ? _value.startedAt
            : startedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        completedAt: freezed == completedAt
            ? _value.completedAt
            : completedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc

class _$MemorizationSessionImpl implements _MemorizationSession {
  const _$MemorizationSessionImpl({
    required this.id,
    required this.textId,
    required final List<String> words,
    this.currentWordIndex = 0,
    final List<String> completedWords = const [],
    this.isCompleted = false,
    required this.startedAt,
    this.completedAt,
  }) : _words = words,
       _completedWords = completedWords;

  @override
  final String id;
  @override
  final String textId;
  final List<String> _words;
  @override
  List<String> get words {
    if (_words is EqualUnmodifiableListView) return _words;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_words);
  }

  @override
  @JsonKey()
  final int currentWordIndex;
  final List<String> _completedWords;
  @override
  @JsonKey()
  List<String> get completedWords {
    if (_completedWords is EqualUnmodifiableListView) return _completedWords;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_completedWords);
  }

  @override
  @JsonKey()
  final bool isCompleted;
  @override
  final DateTime startedAt;
  @override
  final DateTime? completedAt;

  @override
  String toString() {
    return 'MemorizationSession(id: $id, textId: $textId, words: $words, currentWordIndex: $currentWordIndex, completedWords: $completedWords, isCompleted: $isCompleted, startedAt: $startedAt, completedAt: $completedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MemorizationSessionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.textId, textId) || other.textId == textId) &&
            const DeepCollectionEquality().equals(other._words, _words) &&
            (identical(other.currentWordIndex, currentWordIndex) ||
                other.currentWordIndex == currentWordIndex) &&
            const DeepCollectionEquality().equals(
              other._completedWords,
              _completedWords,
            ) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    textId,
    const DeepCollectionEquality().hash(_words),
    currentWordIndex,
    const DeepCollectionEquality().hash(_completedWords),
    isCompleted,
    startedAt,
    completedAt,
  );

  /// Create a copy of MemorizationSession
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MemorizationSessionImplCopyWith<_$MemorizationSessionImpl> get copyWith =>
      __$$MemorizationSessionImplCopyWithImpl<_$MemorizationSessionImpl>(
        this,
        _$identity,
      );
}

abstract class _MemorizationSession implements MemorizationSession {
  const factory _MemorizationSession({
    required final String id,
    required final String textId,
    required final List<String> words,
    final int currentWordIndex,
    final List<String> completedWords,
    final bool isCompleted,
    required final DateTime startedAt,
    final DateTime? completedAt,
  }) = _$MemorizationSessionImpl;

  @override
  String get id;
  @override
  String get textId;
  @override
  List<String> get words;
  @override
  int get currentWordIndex;
  @override
  List<String> get completedWords;
  @override
  bool get isCompleted;
  @override
  DateTime get startedAt;
  @override
  DateTime? get completedAt;

  /// Create a copy of MemorizationSession
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MemorizationSessionImplCopyWith<_$MemorizationSessionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
