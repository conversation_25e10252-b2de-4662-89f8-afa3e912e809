import 'package:dartz/dartz.dart';
import 'package:uuid/uuid.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/errors/exceptions.dart' as app_exceptions;
import '../../../../core/extensions/string_extensions.dart';
import '../../domain/entities/memorization_session.dart';
import '../../domain/entities/word_match_result.dart';
import '../../domain/repositories/memorization_repository.dart';
import '../datasources/speech_recognition_datasource.dart';

class MemorizationRepositoryImpl implements MemorizationRepository {
  final SpeechRecognitionDatasource speechDatasource;
  final Uuid _uuid = const Uuid();

  MemorizationRepositoryImpl(this.speechDatasource);

  @override
  Future<Either<Failure, MemorizationSession>> startSession(
    String textId,
    List<String> words,
  ) async {
    try {
      // Initialize speech recognition (this will handle availability and permissions)
      await speechDatasource.initialize();

      final session = MemorizationSession(
        id: _uuid.v4(),
        textId: textId,
        words: words,
        startedAt: DateTime.now(),
      );
      return Right(session);
    } on app_exceptions.SpeechRecognitionException catch (e) {
      return Left(Failure.speechRecognition(message: e.message, code: e.code));
    } on app_exceptions.PermissionException catch (e) {
      return Left(Failure.permission(message: e.message, code: e.code));
    } catch (e) {
      return Left(Failure.unknown(message: 'فشل في بدء الجلسة: $e'));
    }
  }

  @override
  Future<Either<Failure, WordMatchResult>> processSpokenWord(
    String spokenWord,
    String expectedWord,
  ) async {
    try {
      final isMatch = _validateArabicWords(spokenWord, expectedWord);

      final result = WordMatchResult(
        isMatch: isMatch,
        expectedWord: expectedWord,
        spokenWord: spokenWord,
        timestamp: DateTime.now(),
      );

      return Right(result);
    } catch (e) {
      return Left(
        Failure.speechRecognition(message: 'Failed to process speech: $e'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> validateWord(
    String spokenWord,
    String expectedWord,
  ) async {
    try {
      final isMatch = _validateArabicWords(spokenWord, expectedWord);
      return Right(isMatch);
    } catch (e) {
      return Left(Failure.unknown(message: 'Failed to validate word: $e'));
    }
  }

  /// Validates Arabic words with strict matching including diacritics
  bool _validateArabicWords(String spokenWord, String expectedWord) {
    // Clean up the words
    final cleanSpoken = spokenWord.trim();
    final cleanExpected = expectedWord.trim();

    // First try exact match (including diacritics)
    if (cleanSpoken.isExactArabicMatch(cleanExpected)) {
      return true;
    }

    // For now, we'll also accept matches without diacritics
    // This can be made configurable later based on user preference
    if (cleanSpoken.isArabicMatchWithoutDiacritics(cleanExpected)) {
      return true;
    }

    return false;
  }
}
