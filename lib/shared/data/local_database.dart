import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';
import 'package:path/path.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../../core/constants/app_constants.dart';
import '../../core/constants/storage_keys.dart';

class LocalDatabase {
  static final LocalDatabase _instance = LocalDatabase._internal();
  static Database? _database;

  LocalDatabase._internal() {
    _initializeDatabaseFactory();
  }

  static LocalDatabase get instance => _instance;

  void _initializeDatabaseFactory() {
    // Initialize sqflite for different platforms
    if (kIsWeb) {
      // Web platform
      databaseFactory = databaseFactoryFfiWeb;
    } else {
      try {
        if (Platform.isLinux || Platform.isWindows || Platform.isMacOS) {
          // Desktop platforms
          sqfliteFfiInit();
          databaseFactory = databaseFactoryFfi;
        }
        // Mobile platforms (Android/iOS) use default sqflite
      } catch (e) {
        // Fallback to default database factory
      }
    }
  }

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await _createSavedTextsTable(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add upgrade logic for version 2
    }
  }

  Future<void> _createSavedTextsTable(Database db) async {
    await db.execute('''
      CREATE TABLE ${AppConstants.savedTextsTable} (
        ${StorageKeys.savedTextId} TEXT PRIMARY KEY,
        ${StorageKeys.savedTextTitle} TEXT NOT NULL,
        ${StorageKeys.savedTextContent} TEXT NOT NULL,
        ${StorageKeys.savedTextCreatedAt} INTEGER NOT NULL,
        ${StorageKeys.savedTextUpdatedAt} INTEGER NOT NULL
      )
    ''');
  }

  Future<void> closeDatabase() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }

  Future<void> deleteDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);
    await databaseFactory.deleteDatabase(path);
    _database = null;
  }
}
