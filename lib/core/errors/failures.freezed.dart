// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$Failure {
  String get message => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FailureCopyWith<Failure> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FailureCopyWith<$Res> {
  factory $FailureCopyWith(Failure value, $Res Function(Failure) then) =
      _$FailureCopyWithImpl<$Res, Failure>;
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class _$FailureCopyWithImpl<$Res, $Val extends Failure>
    implements $FailureCopyWith<$Res> {
  _$FailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _value.copyWith(
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
            code: freezed == code
                ? _value.code
                : code // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DatabaseFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$DatabaseFailureImplCopyWith(
    _$DatabaseFailureImpl value,
    $Res Function(_$DatabaseFailureImpl) then,
  ) = __$$DatabaseFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$DatabaseFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$DatabaseFailureImpl>
    implements _$$DatabaseFailureImplCopyWith<$Res> {
  __$$DatabaseFailureImplCopyWithImpl(
    _$DatabaseFailureImpl _value,
    $Res Function(_$DatabaseFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$DatabaseFailureImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$DatabaseFailureImpl implements DatabaseFailure {
  const _$DatabaseFailureImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.database(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DatabaseFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DatabaseFailureImplCopyWith<_$DatabaseFailureImpl> get copyWith =>
      __$$DatabaseFailureImplCopyWithImpl<_$DatabaseFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) {
    return database(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) {
    return database?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) {
    if (database != null) {
      return database(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) {
    return database(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) {
    return database?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (database != null) {
      return database(this);
    }
    return orElse();
  }
}

abstract class DatabaseFailure implements Failure {
  const factory DatabaseFailure({
    required final String message,
    final String? code,
  }) = _$DatabaseFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DatabaseFailureImplCopyWith<_$DatabaseFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SpeechRecognitionFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$SpeechRecognitionFailureImplCopyWith(
    _$SpeechRecognitionFailureImpl value,
    $Res Function(_$SpeechRecognitionFailureImpl) then,
  ) = __$$SpeechRecognitionFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$SpeechRecognitionFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$SpeechRecognitionFailureImpl>
    implements _$$SpeechRecognitionFailureImplCopyWith<$Res> {
  __$$SpeechRecognitionFailureImplCopyWithImpl(
    _$SpeechRecognitionFailureImpl _value,
    $Res Function(_$SpeechRecognitionFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$SpeechRecognitionFailureImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$SpeechRecognitionFailureImpl implements SpeechRecognitionFailure {
  const _$SpeechRecognitionFailureImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.speechRecognition(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SpeechRecognitionFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SpeechRecognitionFailureImplCopyWith<_$SpeechRecognitionFailureImpl>
  get copyWith =>
      __$$SpeechRecognitionFailureImplCopyWithImpl<
        _$SpeechRecognitionFailureImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) {
    return speechRecognition(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) {
    return speechRecognition?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) {
    if (speechRecognition != null) {
      return speechRecognition(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) {
    return speechRecognition(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) {
    return speechRecognition?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (speechRecognition != null) {
      return speechRecognition(this);
    }
    return orElse();
  }
}

abstract class SpeechRecognitionFailure implements Failure {
  const factory SpeechRecognitionFailure({
    required final String message,
    final String? code,
  }) = _$SpeechRecognitionFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SpeechRecognitionFailureImplCopyWith<_$SpeechRecognitionFailureImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PermissionFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$PermissionFailureImplCopyWith(
    _$PermissionFailureImpl value,
    $Res Function(_$PermissionFailureImpl) then,
  ) = __$$PermissionFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$PermissionFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$PermissionFailureImpl>
    implements _$$PermissionFailureImplCopyWith<$Res> {
  __$$PermissionFailureImplCopyWithImpl(
    _$PermissionFailureImpl _value,
    $Res Function(_$PermissionFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$PermissionFailureImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$PermissionFailureImpl implements PermissionFailure {
  const _$PermissionFailureImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.permission(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PermissionFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PermissionFailureImplCopyWith<_$PermissionFailureImpl> get copyWith =>
      __$$PermissionFailureImplCopyWithImpl<_$PermissionFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) {
    return permission(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) {
    return permission?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) {
    return permission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) {
    return permission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(this);
    }
    return orElse();
  }
}

abstract class PermissionFailure implements Failure {
  const factory PermissionFailure({
    required final String message,
    final String? code,
  }) = _$PermissionFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PermissionFailureImplCopyWith<_$PermissionFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AudioFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$AudioFailureImplCopyWith(
    _$AudioFailureImpl value,
    $Res Function(_$AudioFailureImpl) then,
  ) = __$$AudioFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$AudioFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$AudioFailureImpl>
    implements _$$AudioFailureImplCopyWith<$Res> {
  __$$AudioFailureImplCopyWithImpl(
    _$AudioFailureImpl _value,
    $Res Function(_$AudioFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$AudioFailureImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$AudioFailureImpl implements AudioFailure {
  const _$AudioFailureImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.audio(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AudioFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AudioFailureImplCopyWith<_$AudioFailureImpl> get copyWith =>
      __$$AudioFailureImplCopyWithImpl<_$AudioFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) {
    return audio(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) {
    return audio?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) {
    if (audio != null) {
      return audio(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) {
    return audio(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) {
    return audio?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (audio != null) {
      return audio(this);
    }
    return orElse();
  }
}

abstract class AudioFailure implements Failure {
  const factory AudioFailure({
    required final String message,
    final String? code,
  }) = _$AudioFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AudioFailureImplCopyWith<_$AudioFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TextValidationFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$TextValidationFailureImplCopyWith(
    _$TextValidationFailureImpl value,
    $Res Function(_$TextValidationFailureImpl) then,
  ) = __$$TextValidationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$TextValidationFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$TextValidationFailureImpl>
    implements _$$TextValidationFailureImplCopyWith<$Res> {
  __$$TextValidationFailureImplCopyWithImpl(
    _$TextValidationFailureImpl _value,
    $Res Function(_$TextValidationFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$TextValidationFailureImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$TextValidationFailureImpl implements TextValidationFailure {
  const _$TextValidationFailureImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.textValidation(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TextValidationFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TextValidationFailureImplCopyWith<_$TextValidationFailureImpl>
  get copyWith =>
      __$$TextValidationFailureImplCopyWithImpl<_$TextValidationFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) {
    return textValidation(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) {
    return textValidation?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) {
    if (textValidation != null) {
      return textValidation(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) {
    return textValidation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) {
    return textValidation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (textValidation != null) {
      return textValidation(this);
    }
    return orElse();
  }
}

abstract class TextValidationFailure implements Failure {
  const factory TextValidationFailure({
    required final String message,
    final String? code,
  }) = _$TextValidationFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TextValidationFailureImplCopyWith<_$TextValidationFailureImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NetworkFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$NetworkFailureImplCopyWith(
    _$NetworkFailureImpl value,
    $Res Function(_$NetworkFailureImpl) then,
  ) = __$$NetworkFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$NetworkFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$NetworkFailureImpl>
    implements _$$NetworkFailureImplCopyWith<$Res> {
  __$$NetworkFailureImplCopyWithImpl(
    _$NetworkFailureImpl _value,
    $Res Function(_$NetworkFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$NetworkFailureImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$NetworkFailureImpl implements NetworkFailure {
  const _$NetworkFailureImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.network(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkFailureImplCopyWith<_$NetworkFailureImpl> get copyWith =>
      __$$NetworkFailureImplCopyWithImpl<_$NetworkFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) {
    return network(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) {
    return network?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) {
    return network(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) {
    return network?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(this);
    }
    return orElse();
  }
}

abstract class NetworkFailure implements Failure {
  const factory NetworkFailure({
    required final String message,
    final String? code,
  }) = _$NetworkFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkFailureImplCopyWith<_$NetworkFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CacheFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$CacheFailureImplCopyWith(
    _$CacheFailureImpl value,
    $Res Function(_$CacheFailureImpl) then,
  ) = __$$CacheFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$CacheFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$CacheFailureImpl>
    implements _$$CacheFailureImplCopyWith<$Res> {
  __$$CacheFailureImplCopyWithImpl(
    _$CacheFailureImpl _value,
    $Res Function(_$CacheFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$CacheFailureImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$CacheFailureImpl implements CacheFailure {
  const _$CacheFailureImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.cache(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CacheFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CacheFailureImplCopyWith<_$CacheFailureImpl> get copyWith =>
      __$$CacheFailureImplCopyWithImpl<_$CacheFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) {
    return cache(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) {
    return cache?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) {
    if (cache != null) {
      return cache(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) {
    return cache(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) {
    return cache?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (cache != null) {
      return cache(this);
    }
    return orElse();
  }
}

abstract class CacheFailure implements Failure {
  const factory CacheFailure({
    required final String message,
    final String? code,
  }) = _$CacheFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CacheFailureImplCopyWith<_$CacheFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnknownFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$UnknownFailureImplCopyWith(
    _$UnknownFailureImpl value,
    $Res Function(_$UnknownFailureImpl) then,
  ) = __$$UnknownFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$UnknownFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$UnknownFailureImpl>
    implements _$$UnknownFailureImplCopyWith<$Res> {
  __$$UnknownFailureImplCopyWithImpl(
    _$UnknownFailureImpl _value,
    $Res Function(_$UnknownFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$UnknownFailureImpl(
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        code: freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$UnknownFailureImpl implements UnknownFailure {
  const _$UnknownFailureImpl({required this.message, this.code});

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.unknown(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnknownFailureImplCopyWith<_$UnknownFailureImpl> get copyWith =>
      __$$UnknownFailureImplCopyWithImpl<_$UnknownFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) speechRecognition,
    required TResult Function(String message, String? code) permission,
    required TResult Function(String message, String? code) audio,
    required TResult Function(String message, String? code) textValidation,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) cache,
    required TResult Function(String message, String? code) unknown,
  }) {
    return unknown(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? speechRecognition,
    TResult? Function(String message, String? code)? permission,
    TResult? Function(String message, String? code)? audio,
    TResult? Function(String message, String? code)? textValidation,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? cache,
    TResult? Function(String message, String? code)? unknown,
  }) {
    return unknown?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? speechRecognition,
    TResult Function(String message, String? code)? permission,
    TResult Function(String message, String? code)? audio,
    TResult Function(String message, String? code)? textValidation,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? cache,
    TResult Function(String message, String? code)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(SpeechRecognitionFailure value) speechRecognition,
    required TResult Function(PermissionFailure value) permission,
    required TResult Function(AudioFailure value) audio,
    required TResult Function(TextValidationFailure value) textValidation,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(CacheFailure value) cache,
    required TResult Function(UnknownFailure value) unknown,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult? Function(PermissionFailure value)? permission,
    TResult? Function(AudioFailure value)? audio,
    TResult? Function(TextValidationFailure value)? textValidation,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(CacheFailure value)? cache,
    TResult? Function(UnknownFailure value)? unknown,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(SpeechRecognitionFailure value)? speechRecognition,
    TResult Function(PermissionFailure value)? permission,
    TResult Function(AudioFailure value)? audio,
    TResult Function(TextValidationFailure value)? textValidation,
    TResult Function(NetworkFailure value)? network,
    TResult Function(CacheFailure value)? cache,
    TResult Function(UnknownFailure value)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class UnknownFailure implements Failure {
  const factory UnknownFailure({
    required final String message,
    final String? code,
  }) = _$UnknownFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnknownFailureImplCopyWith<_$UnknownFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
