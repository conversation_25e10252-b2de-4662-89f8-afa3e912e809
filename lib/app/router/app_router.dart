import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import '../../features/text_management/presentation/pages/add_text_page.dart';
import '../../features/text_management/presentation/pages/text_library_page.dart';
import '../../features/memorization/presentation/pages/memorization_session_page.dart';
import '../../features/text_management/domain/entities/saved_text.dart';

part 'app_router.gr.dart';

@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  @override
  List<AutoRoute> get routes => [
    // Text Library (Home) Route
    AutoRoute(page: TextLibraryRoute.page, path: '/', initial: true),

    // Add Text Route
    AutoRoute(page: AddTextRoute.page, path: '/add-text'),

    // Memorization Session Route
    AutoRoute(
      page: MemorizationSessionRoute.page,
      path: '/memorization-session',
    ),
  ];
}

@RoutePage()
class TextLibraryPage extends StatelessWidget {
  const TextLibraryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const TextLibraryPageWidget();
  }
}

@RoutePage()
class AddTextPage extends StatelessWidget {
  const AddTextPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const AddTextPageWidget();
  }
}

@RoutePage()
class MemorizationSessionPage extends StatelessWidget {
  final SavedText savedText;

  const MemorizationSessionPage({super.key, required this.savedText});

  @override
  Widget build(BuildContext context) {
    return MemorizationSessionPageWidget(savedText: savedText);
  }
}
