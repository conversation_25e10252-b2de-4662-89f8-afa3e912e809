// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

part of 'app_router.dart';

/// generated route for
/// [AddTextPage]
class AddTextRoute extends PageRouteInfo<void> {
  const AddTextRoute({List<PageRouteInfo>? children})
    : super(AddTextRoute.name, initialChildren: children);

  static const String name = 'AddTextRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const AddTextPage();
    },
  );
}

/// generated route for
/// [MemorizationSessionPage]
class MemorizationSessionRoute
    extends PageRouteInfo<MemorizationSessionRouteArgs> {
  MemorizationSessionRoute({
    Key? key,
    required SavedText savedText,
    List<PageRouteInfo>? children,
  }) : super(
         MemorizationSessionRoute.name,
         args: MemorizationSessionRouteArgs(key: key, savedText: savedText),
         initialChildren: children,
       );

  static const String name = 'MemorizationSessionRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<MemorizationSessionRouteArgs>();
      return MemorizationSessionPage(key: args.key, savedText: args.savedText);
    },
  );
}

class MemorizationSessionRouteArgs {
  const MemorizationSessionRouteArgs({this.key, required this.savedText});

  final Key? key;

  final SavedText savedText;

  @override
  String toString() {
    return 'MemorizationSessionRouteArgs{key: $key, savedText: $savedText}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! MemorizationSessionRouteArgs) return false;
    return key == other.key && savedText == other.savedText;
  }

  @override
  int get hashCode => key.hashCode ^ savedText.hashCode;
}

/// generated route for
/// [TextLibraryPage]
class TextLibraryRoute extends PageRouteInfo<void> {
  const TextLibraryRoute({List<PageRouteInfo>? children})
    : super(TextLibraryRoute.name, initialChildren: children);

  static const String name = 'TextLibraryRoute';

  static PageInfo page = PageInfo(
    name,
    builder: (data) {
      return const TextLibraryPage();
    },
  );
}
